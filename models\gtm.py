# 文件: models/gtm.py
# 门控轨迹混合器 (Gated Track Mixer) - 第4步实现

import torch
from torch import nn
# 导入checkpoint函数
from torch.utils.checkpoint import checkpoint

# 尝试导入mamba-ssm，如果失败则使用简化版本
try:
    from mamba_ssm import Mamba
    MAMBA_AVAILABLE = True
except ImportError:
    MAMBA_AVAILABLE = False
    print("⚠️  mamba-ssm 不可用，使用简化的GTM实现")


class SimpleMamba(nn.Module):
    """简化的Mamba实现，用于GTM"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2):
        super().__init__()
        self.d_model = d_model
        self.linear1 = nn.Linear(d_model, d_model * expand)
        self.linear2 = nn.Linear(d_model * expand, d_model)
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # 简化的前向传播
        residual = x
        x = self.norm(x)
        x = self.linear1(x)
        x = torch.relu(x)
        x = self.linear2(x)
        return x + residual


class GatedTrackMixer(nn.Module):
    """
    门控轨迹混合器 (GTM)
    
    这个模块促进轨迹嵌入之间的通信，采用计算高效且可控的方式。
    它使用双向Mamba来混合信息，并通过置信度控制的门控机制进行调节。
    """
    
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, use_checkpoint=False):
        super().__init__()
        self.d_model = d_model
        # 存储检查点决策
        self.use_checkpoint = use_checkpoint
        
        # Mamba 参数参考了标准实现，d_state=16在效率和性能间取得良好平衡
        if MAMBA_AVAILABLE:
            self.forward_mamba = Mamba(d_model=d_model, d_state=d_state, d_conv=d_conv, expand=expand)
            self.backward_mamba = Mamba(d_model=d_model, d_state=d_state, d_conv=d_conv, expand=expand)
            print(f"✅ GTM 使用官方 mamba-ssm (d_state={d_state})")
        else:
            self.forward_mamba = SimpleMamba(d_model=d_model, d_state=d_state, d_conv=d_conv, expand=expand)
            self.backward_mamba = SimpleMamba(d_model=d_model, d_state=d_state, d_conv=d_conv, expand=expand)
            print(f"⚠️  GTM 使用简化实现 (d_model={d_model})")

        # 门控网络：将CIFE输出的单个置信度分数映射为一个门控向量
        self.gating_mlp = nn.Sequential(
            nn.Linear(1, d_model // 4),  # 从1维置信度开始
            nn.ReLU(),
            nn.Linear(d_model // 4, d_model),
            nn.Sigmoid()  # 输出0-1之间的门控权重
        )
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

    def forward(self, track_embeds, confidence_scores):
        """
        Args:
            track_embeds (Tensor): 当前轨迹嵌入 [num_track_slots, D]
            confidence_scores (Tensor): 与每个轨迹关联的观测置信度 [num_track_slots, 1]
                                       对于未关联的轨迹，这应该是0
        Returns:
            Tensor: 混合后的轨迹嵌入 [num_track_slots, D]
        """
        # 1. 残差连接的起点
        residual = track_embeds

        # 2. 归一化输入
        track_embeds = self.norm1(track_embeds)
        
        # 3. 计算门控权重
        gates = self.gating_mlp(confidence_scores)
        
        # 4. 应用门控：置信度低的轨道在混合中的"音量"会变小
        gated_embeds = track_embeds * gates
        
        # 5. 双向Mamba混合
        # Mamba需要 (B, L, D) 输入, 我们这里 B=1, L=num_track_slots
        gated_embeds_seq = gated_embeds.unsqueeze(0)

        # === 关键修改：使用梯度检查点 ===
        if self.training and self.use_checkpoint:
            # 使用checkpoint包装Mamba调用，避免存储中间激活值
            print("🔧 GTM使用梯度检查点")  # 调试信息
            forward_out = checkpoint(self.forward_mamba, gated_embeds_seq, use_reentrant=False)

            # 反向处理
            reversed_embeds_seq = torch.flip(gated_embeds_seq, dims=[1])
            reversed_out = checkpoint(self.backward_mamba, reversed_embeds_seq, use_reentrant=False)
            backward_out = torch.flip(reversed_out, dims=[1])
        else:
            # 正常前向传播（推理时或禁用时）
            forward_out = self.forward_mamba(gated_embeds_seq)

            # 反向处理
            reversed_embeds_seq = torch.flip(gated_embeds_seq, dims=[1])
            reversed_out = self.backward_mamba(reversed_embeds_seq)
            backward_out = torch.flip(reversed_out, dims=[1])
        
        # 6. 融合输出并进行第二次归一化
        mixed_embeds = self.norm2(forward_out + backward_out).squeeze(0)
        
        # 7. 添加残差连接
        return residual + mixed_embeds


if __name__ == "__main__":
    # 测试GTM模块
    print("🧪 测试 GTM 模块...")
    
    d_model = 256
    num_track_slots = 500
    
    gtm = GatedTrackMixer(d_model=d_model)
    
    # 创建测试数据
    track_embeds = torch.randn(num_track_slots, d_model)
    confidence_scores = torch.rand(num_track_slots, 1)
    
    # 前向传播
    mixed_embeds = gtm(track_embeds, confidence_scores)
    
    print(f"✅ GTM 测试成功:")
    print(f"   - 输入形状: {track_embeds.shape}")
    print(f"   - 输出形状: {mixed_embeds.shape}")
    print(f"   - 置信度形状: {confidence_scores.shape}")
