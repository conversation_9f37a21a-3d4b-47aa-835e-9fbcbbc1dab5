# ====================================================================
# START: Final content for models/cift.py - Method 1 (Hybrid Architecture)
# ====================================================================
import torch
import torch.nn as nn
import torchvision.ops.roi_align as roi_align

# 尝试导入mamba-ssm，如果失败则使用简化版本
try:
    from mamba_ssm import Mamba
    MAMBA_AVAILABLE = True
    print("✅ 使用官方 mamba-ssm")
except ImportError:
    MAMBA_AVAILABLE = False
    print("⚠️  mamba-ssm 不可用，使用简化版本")
    from .mamba_block import MambaBlock

from util import box_ops  # 确保你的项目中这个导入路径是正确的
from .gtm_deformable import GTM

class CIFT(nn.Module):
    """
    Corrected CIFT: A sub-module for temporal query updating and subsequent localization.
    It receives features, it does not create them.
    """
    def __init__(self, d_model=256, nhead=8, num_decoder_layers=6, dim_feedforward=1024, dropout=0.1):
        super().__init__()

        self.d_model = d_model

        # 验证：这里绝对不应该有 backbone 或 encoder 的初始化
        print("Initializing Corrected CIFT module. No backbone here.")

        # 1. GTM for localization in the current frame (Deformable Transformer-based)
        self.gtm = GTM(
            d_model=d_model, nhead=nhead, num_decoder_layers=num_decoder_layers,
            dim_feedforward=dim_feedforward, dropout=dropout
        )

        # 2. Mamba module for temporal update
        if MAMBA_AVAILABLE:
            self.query_updater = Mamba(d_model=d_model)
        else:
            self.query_updater = MambaBlock(d_model=d_model)

        # 3. LayerNorm for stabilizing inputs
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

    def forward(self,
                track_instances,                 # 上一帧的追踪实例
                current_enhanced_features,       # 当前帧增强后的特征图 M'_t [HW, B, D]
                last_enhanced_features=None,     # 上一帧增强后的特征图 M'_{t-1} [HW, B, D]
                current_pos=None,                # 当前帧的位置编码 [HW, B, D]
                feature_shape=None):             # 特征图的 (H, W)

        if track_instances is None or len(track_instances) == 0:
            return track_instances # 如果没有轨迹，无需处理

        queries = track_instances.query_embed # Shape: [num_tracks, D]

        # ---- Temporal Update using Mamba ----
        if last_enhanced_features is not None:
            last_pred_boxes = track_instances.pred_boxes # Shape: [num_tracks, 4] (cxcywh)

            # --- 1. RoIAlign to get last frame's appearance ---
            h, w = feature_shape
            # For RoIAlign, features need to be [B, D, H, W]
            bs = current_enhanced_features.shape[1]
            last_features_2d = last_enhanced_features.permute(1, 2, 0).view(bs, self.d_model, h, w)

            box_xyxy = box_ops.box_cxcywh_to_xyxy(last_pred_boxes)
            # Create batch indices for RoIAlign
            num_tracks = box_xyxy.shape[0]
            box_indices = torch.zeros(num_tracks, device=box_xyxy.device, dtype=torch.float32)
            if bs > 1:
                # Handle multiple batches if needed
                box_indices = torch.arange(bs, device=box_xyxy.device).repeat_interleave(num_tracks // bs)
            rois = torch.cat([box_indices.unsqueeze(-1), box_xyxy], dim=1)

            # spatial_scale should be 1.0 / backbone_stride
            roi_feat = roi_align(last_features_2d, rois, output_size=(1, 1), spatial_scale=1.0/32.0, aligned=True)
            roi_feat = roi_feat.flatten(1) # Shape: [num_tracks, D]

            # --- 2. Fuse and Update with Mamba ---
            fused_input = self.norm1(queries + roi_feat)

            if MAMBA_AVAILABLE:
                # Prepare input for Mamba: (B, L, D) -> [num_tracks, 1, D]
                mamba_input = fused_input.unsqueeze(1) # Shape: [num_tracks, 1, D]
                # Mamba processes the input and updates the query
                mamba_output = self.query_updater(mamba_input) # Shape: [num_tracks, 1, D]
                # Update queries with a residual connection
                queries = queries + mamba_output.squeeze(1) # Shape: [num_tracks, D]
            else:
                # Use simplified MambaBlock
                queries = queries + self.query_updater(fused_input)

        # ---- Localization in Current Frame using Deformable GTM ----
        # 准备Deformable GTM的输入
        bs = current_enhanced_features.shape[1]
        queries_for_gtm = queries.unsqueeze(1).repeat(1, bs, 1) # [num_tracks, B, D]

        # 为Deformable GTM准备参数
        # 如果可变形注意力可用，需要准备reference_points等参数
        try:
            # 尝试使用Deformable GTM的完整功能
            h, w = feature_shape if feature_shape else (20, 20)  # 默认特征图尺寸
            memory_spatial_shapes = torch.tensor([[h, w]], device=queries.device, dtype=torch.long)
            memory_level_start_index = torch.tensor([0], device=queries.device, dtype=torch.long)

            # 创建reference points (简化版)
            reference_points = torch.rand(queries_for_gtm.shape[0], queries_for_gtm.shape[1], 2, device=queries.device)

            gtm_output_layers = self.gtm(
                tgt=queries_for_gtm,
                reference_points=reference_points,
                memory=current_enhanced_features,
                memory_spatial_shapes=memory_spatial_shapes,
                memory_level_start_index=memory_level_start_index,
                query_pos=None
            )
        except Exception as e:
            # 如果Deformable GTM失败，使用标准Transformer调用
            print(f"⚠️  Deformable GTM调用失败，使用标准模式: {e}")
            gtm_output_layers = self.gtm(
                tgt=queries_for_gtm,
                memory=current_enhanced_features,
                query_pos=None
            )

        # Take the output from the last GTM layer
        if isinstance(gtm_output_layers, torch.Tensor):
            final_queries = self.norm2(gtm_output_layers)
        else:
            final_queries = self.norm2(gtm_output_layers[-1]) # Shape: [num_tracks, B, D]

        # Update track_instances. Assuming batch size is 1 for simplicity
        track_instances.output_embedding = final_queries.squeeze(1) if bs == 1 else final_queries[:, 0, :]

        return track_instances

# ====================================================================
# END: Final content for models/cift.py - Method 1 (Hybrid Architecture)
# ====================================================================

def build_cift(args):
    """
    构建 CIFT (Causal Instance Filtering Tracker) 模型

    注意：这个函数现在返回的是CIM Hybrid模型，因为CIFT已经集成到CIM Hybrid中
    """
    # 直接调用CIM Hybrid的构建函数
    from .cim_hybrid import build as build_cim_hybrid
    return build_cim_hybrid(args)
