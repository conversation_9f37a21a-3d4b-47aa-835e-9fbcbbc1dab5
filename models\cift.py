# ====================================================================
# START: new content for models/cift.py
# ====================================================================
import torch
import torch.nn as nn
import torchvision.ops.roi_align as roi_align

from util import box_ops  # 确保你的项目中这个导入路径是正确的
from .gtm import GatedTrackMixer
from .mamba_block import MambaBlock

class CIFT(nn.Module):
    """
    Corrected CIM-based Feature Interaction and Temporal fusion module.
    This module is NOT a complete model. It's a sub-module responsible for:
    1. Updating track queries using historical appearance features.
    2. Calling the GTM to localize tracks in the current frame.
    """
    def __init__(self, d_model=256, nhead=8, num_decoder_layers=6, dim_feedforward=1024, dropout=0.1):
        super().__init__()

        # 验证：这里绝对不应该有 backbone 或 encoder 的初始化
        print("Initializing Corrected CIFT module. No backbone here.")

        # GTM 用于在当前帧进行定位
        self.gtm = GatedTrackMixer(
            d_model=d_model,
            use_checkpoint=False  # 可以根据需要调整
        )

        # Mamba 模块用于融合历史外观和抽象查询
        self.query_updater = MambaBlock(d_model=d_model)

        self.d_model = d_model
        # LayerNorm 可能有帮助
        self.norm = nn.LayerNorm(d_model)

    def forward(self,
                track_instances,                 # 上一帧的追踪实例
                current_enhanced_features,       # 当前帧增强后的特征图 M'_t [HW, B, D]
                last_enhanced_features,          # 上一帧增强后的特征图 M'_{t-1} [HW, B, D]
                current_pos,                     # 当前帧的位置编码 [HW, B, D]
                feature_shape):                  # 特征图的 (H, W)

        # 如果没有追踪实例，直接返回
        if track_instances is None or len(track_instances) == 0:
            return track_instances

        # ---- 核心逻辑：时序更新 ----
        # 仅当有历史信息时，才执行时序更新
        if last_enhanced_features is not None:
            last_query_embed = track_instances.query_embed  # [num_tracks, D]
            last_pred_boxes = track_instances.pred_boxes    # [num_tracks, 4] (cxcywh)

            # 1. RoIAlign: 从上一帧特征图中提取精确的外观特征
            h, w = feature_shape
            last_features_2d = last_enhanced_features.permute(1, 2, 0).view(last_enhanced_features.shape[1], self.d_model, h, w)
            box_xyxy = box_ops.box_cxcywh_to_xyxy(last_pred_boxes)
            box_indices = torch.arange(box_xyxy.shape[0], device=box_xyxy.device).float() % last_features_2d.shape[0] # Handle batch size > 1
            rois = torch.cat([box_indices.unsqueeze(-1), box_xyxy], dim=1)

            # 使用 RoIAlign 提取特征并展平
            # spatial_scale: 输入坐标与特征图坐标的比例。如果特征图是原图的 1/32, scale=1/32
            # 假设你的 backbone 总 stride=32
            roi_feat = roi_align(last_features_2d, rois, output_size=(1, 1), spatial_scale=1.0/32.0, aligned=True)
            roi_feat = roi_feat.flatten(1) # [num_tracks, D]

            # 2. Mamba 更新 Query
            # 将抽象身份(last_query_embed)与上一帧的外观(roi_feat)融合
            # 这是一个简单但有效的融合策略
            updated_query = self.query_updater(self.norm(last_query_embed + roi_feat))
            track_instances.query_embed = updated_query

        # ---- 核心逻辑：当前帧定位 ----
        # 3. GTM 定位
        # **关键修正**：GTM 的 `memory` 输入永远是 *当前帧* 的特征图 `current_enhanced_features`
        # GTM 的输入 tgt 应该是 (L, B, D) 格式, L 是序列长度
        queries_for_gtm = track_instances.query_embed.unsqueeze(1) # [num_tracks, 1, D]

        # 准备置信度信号 (初始化为零)
        track_confidence = torch.zeros(
            track_instances.query_embed.shape[0], 1,
            device=track_instances.query_embed.device,
            dtype=track_instances.query_embed.dtype
        )

        # GTM 执行门控混合
        final_queries = self.gtm(track_instances.query_embed, track_confidence)

        # 更新track_instances的query_embed
        track_instances.query_embed = final_queries




