# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-research. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from Deformable DETR (https://github.com/fundamentalvision/Deformable-DETR)
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

"""
CIM Hybrid Model - Transformer + Mamba 混合架构
重命名自 motr.py，现在实现 CIM-Tracker 的混合架构
"""
import copy
import math
import numpy as np
import torch
import torch.nn.functional as F
from torch import nn, Tensor
from typing import List

from util import box_ops, checkpoint
from util.misc import (NestedTensor, nested_tensor_from_tensor_list,
                       accuracy, get_world_size, interpolate, get_rank,
                       is_dist_avail_and_initialized, inverse_sigmoid)

from models.structures import Instances, Boxes, pairwise_iou, matched_boxlist_iou

from .backbone import build_backbone
from .matcher import build_matcher
from .deformable_detr.deformable_detr import SetCriterion, MLP
from .deformable_detr.segmentation import sigmoid_focal_loss
from .cife import CIFE  # 导入新的 CIFE 模块
from .cift import CIFT  # 导入重构后的 CIFT 模块
import torchvision

def pos2posemb(pos, num_pos_feats=64, temperature=10000):
    """位置编码函数，从 deformable_transformer_plus.py 复制"""
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    posemb = pos[..., None] / dim_t
    posemb = torch.stack((posemb[..., 0::2].sin(), posemb[..., 1::2].cos()), dim=-1).flatten(-3)
    return posemb


class CIMHybrid(nn.Module):
    def __init__(self, backbone, transformer, num_classes, num_queries, criterion,
                 aux_loss=True, hidden_dim=256, num_id_vocabulary=500, **kwargs):
        """ Initializes the CIM Hybrid model (Transformer + Mamba) - Advanced Parallel Version.
        Parameters:
            backbone: torch module of the backbone to be used. See backbone.py
            transformer: torch module of the transformer architecture. See transformer.py
            num_classes: number of object classes
            num_queries: number of object queries, ie detection slot. This is the maximal number of objects
                         DETR can detect in a single image. For COCO, we recommend 100 queries.
            aux_loss: True if auxiliary decoding losses (loss at each decoder layer) are to be used.
        """
        super().__init__()
        # --- 1. 维度修正：所有模块统一使用 hidden_dim=256 ---
        self.hidden_dim = hidden_dim
        self.backbone = backbone
        self.transformer = transformer  # 假设它内部的 d_model 也被设为 256
        self.criterion = criterion
        self.num_queries = num_queries
        self.num_id_vocabulary = num_id_vocabulary

        # 正确构建 input_proj
        self.input_proj = nn.ModuleList()
        for in_channels in backbone.num_channels:
            self.input_proj.append(nn.Sequential(
                nn.Conv2d(in_channels, self.hidden_dim, kernel_size=1),
                nn.GroupNorm(32, self.hidden_dim),
            ))

        # 初始化CIFE和CIFT
        self.cife = CIFE(d_model=self.hidden_dim)
        self.cift = CIFT(d_model=self.hidden_dim, dim_feedforward=self.hidden_dim * 8)

        # 初始化预测头
        self.class_embed = nn.Linear(self.hidden_dim, num_id_vocabulary + 1)
        self.bbox_embed = MLP(self.hidden_dim, self.hidden_dim, 4, 3)

        # 添加ROIAlign用于提取ROI特征
        from torchvision.ops import RoIAlign
        self.roi_align = RoIAlign(output_size=(7, 7), spatial_scale=1.0/32.0, sampling_ratio=2)

        self.num_classes = num_classes
        self.yolox_embed = nn.Embedding(1, hidden_dim)
        self.query_embed = nn.Embedding(num_queries, hidden_dim)
        self.position = nn.Embedding(num_queries, 4)

        # 简化参数处理
        self.aux_loss = aux_loss
        self.use_checkpoint = kwargs.get('use_checkpoint', False)
        memory_bank = kwargs.get('memory_bank', None)

        # DETR的输入投影层仍然需要
        num_feature_levels = 4 # 硬编码或从args传入
        self.num_feature_levels = num_feature_levels

        # 确保正确获取backbone的通道数
        if hasattr(backbone, 'num_channels'):
            backbone_channels = backbone.num_channels
        else:
            # 默认ResNet50的通道数
            backbone_channels = [2048]  # 只使用最后一层

        if num_feature_levels > 1:
            num_backbone_outs = len(backbone.strides) if hasattr(backbone, 'strides') else 1
            input_proj_list = []
            for i in range(num_backbone_outs):
                in_channels = backbone_channels[i] if i < len(backbone_channels) else backbone_channels[-1]
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
            for _ in range(num_feature_levels - num_backbone_outs):
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, stride=2, padding=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
            self.input_proj = nn.ModuleList(input_proj_list)
        else:
            # 单尺度：只使用最后一层，通道数应该是2048
            in_channels = backbone_channels[-1] if backbone_channels else 2048
            self.input_proj = nn.ModuleList([
                nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                )])
        self.backbone = backbone
        self.aux_loss = aux_loss
        self.use_checkpoint = use_checkpoint

        for proj in self.input_proj:
            nn.init.xavier_uniform_(proj[0].weight, gain=1)
            nn.init.constant_(proj[0].bias, 0)
        nn.init.uniform_(self.position.weight.data, 0, 1)

        # 导入必要的类
        from .motr import TrackerPostProcess, RuntimeTrackerBase
        self.post_process = TrackerPostProcess()
        self.track_base = RuntimeTrackerBase()
        self.criterion = criterion
        self.memory_bank = memory_bank
        self.mem_bank_len = 0 if memory_bank is None else memory_bank.max_his_length

    def _create_causal_attention_mask(self, T, N, H, W, device):
        """
        创建因果注意力掩膜，确保第t帧的query只能与第t帧的图像特征进行交叉注意力计算

        Args:
            T: 时间步数
            N: 每帧的query数量
            H, W: 特征图的高度和宽度
            device: 设备

        Returns:
            mask: [T*N, T*HW] 的注意力掩膜
        """
        HW = H * W
        total_queries = T * N
        total_keys = T * HW
        mask = torch.full((total_queries, total_keys), float('-inf'), device=device)

        for t in range(T):
            query_start, query_end = t * N, (t + 1) * N
            key_start, key_end = t * HW, (t + 1) * HW
            mask[query_start:query_end, key_start:key_end] = 0

        return mask

    def prepare_clip_queries(self, gt_instances_list, enhanced_memory_flat, T, H, W):
        """
        准备clip级别的queries和ROI特征 (Teacher Forcing)

        Args:
            gt_instances_list: [B][T] 每帧的GT instances
            enhanced_memory_flat: [B*T, HW, D] 增强的特征图
            T: 时间步数
            H, W: 特征图尺寸

        Returns:
            all_gt_queries: [B, T*N, D] GT对应的query embeddings
            all_gt_roi_feats: [B, T*N, D] GT对应的ROI特征
        """
        B = 1  # 假设batch_size=1
        device = enhanced_memory_flat.device

        # 收集所有帧的GT信息
        all_gt_boxes = []
        all_gt_track_ids = []
        max_objects_per_frame = 0

        for t in range(T):
            gt_instances = gt_instances_list[0][t]  # B=1
            boxes = gt_instances.boxes  # [N_t, 4] 格式为 (x1, y1, x2, y2)
            track_ids = gt_instances.obj_ids if hasattr(gt_instances, 'obj_ids') else torch.arange(len(boxes))

            all_gt_boxes.append(boxes)
            all_gt_track_ids.append(track_ids)
            max_objects_per_frame = max(max_objects_per_frame, len(boxes))

        # Padding到统一长度
        N = max_objects_per_frame
        padded_boxes = []
        padded_track_ids = []

        for t in range(T):
            boxes = all_gt_boxes[t]
            track_ids = all_gt_track_ids[t]

            if len(boxes) < N:
                # Padding with dummy boxes
                padding_size = N - len(boxes)
                dummy_boxes = torch.zeros((padding_size, 4), device=device)
                dummy_track_ids = torch.full((padding_size,), -1, device=device)

                boxes = torch.cat([boxes, dummy_boxes], dim=0)
                track_ids = torch.cat([track_ids, dummy_track_ids], dim=0)

            padded_boxes.append(boxes)
            padded_track_ids.append(track_ids)

        # 转换为tensor格式
        all_boxes = torch.stack(padded_boxes)  # [T, N, 4]
        all_track_ids = torch.stack(padded_track_ids)  # [T, N]

        # 准备ROIAlign的输入格式
        roi_list = []
        for t in range(T):
            for n in range(N):
                if all_track_ids[t, n] >= 0:  # 有效的GT
                    # ROIAlign需要的格式: [batch_idx, x1, y1, x2, y2]
                    roi = torch.cat([torch.tensor([t], device=device), all_boxes[t, n]])
                    roi_list.append(roi)

        if len(roi_list) > 0:
            rois = torch.stack(roi_list)  # [total_valid_gts, 5]

            # 重新整理enhanced_memory_flat为特征图格式
            enhanced_memory_reshaped = enhanced_memory_flat.view(T, H, W, -1).permute(0, 3, 1, 2)  # [T, D, H, W]

            # 使用ROIAlign提取特征
            roi_features = self.roi_align(enhanced_memory_reshaped, rois)  # [total_valid_gts, D, 7, 7]
            roi_features = roi_features.mean(dim=[2, 3])  # [total_valid_gts, D] 全局平均池化

            # 重新组织为[B, T*N, D]格式
            all_gt_roi_feats = torch.zeros((B, T*N, self.hidden_dim), device=device)
            roi_idx = 0
            for t in range(T):
                for n in range(N):
                    if all_track_ids[t, n] >= 0:
                        all_gt_roi_feats[0, t*N + n] = roi_features[roi_idx]
                        roi_idx += 1
        else:
            all_gt_roi_feats = torch.zeros((B, T*N, self.hidden_dim), device=device)

        # 准备query embeddings (基于track_id)
        all_gt_queries = torch.zeros((B, T*N, self.hidden_dim), device=device)
        for t in range(T):
            for n in range(N):
                track_id = all_track_ids[t, n]
                if track_id >= 0:
                    # 使用track_id对应的embedding，如果超出范围则使用模运算
                    query_idx = track_id % self.num_queries
                    all_gt_queries[0, t*N + n] = self.query_embed.weight[query_idx]

        return all_gt_queries, all_gt_roi_feats

    def _generate_empty_tracks(self, proposals=None):
        track_instances = Instances((1, 1))
        num_queries, d_model = self.query_embed.weight.shape  # (300, 256)
        device = self.query_embed.weight.device

        # 优化：避免动态分支，使用torch.compile友好的操作
        if proposals is None:
            proposals = torch.empty((0, 8), dtype=torch.float32, device=device)
        elif isinstance(proposals, list):
            if len(proposals) > 0 and isinstance(proposals[0], torch.Tensor):
                proposals = proposals[0]
            else:
                proposals = torch.empty((0, 8), dtype=torch.float32, device=device)

        # 使用torch.where避免条件分支，更compile友好
        has_proposals = proposals.numel() > 0

        if has_proposals:
            track_instances.ref_pts = torch.cat([self.position.weight, proposals[:, :4]])
            track_instances.query_pos = torch.cat([
                self.query_embed.weight,
                pos2posemb(proposals[:, 4:], d_model) + self.yolox_embed.weight
            ])
        else:
            track_instances.ref_pts = self.position.weight
            track_instances.query_pos = self.query_embed.weight
        track_instances.output_embedding = torch.zeros((len(track_instances), d_model), device=device)
        track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.disappear_time = torch.zeros((len(track_instances), ), dtype=torch.long, device=device)
        track_instances.iou = torch.ones((len(track_instances),), dtype=torch.float, device=device)
        track_instances.scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.track_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)

        # 这些将在forward中通过预测头生成，所以这里初始化为零即可
        track_instances.pred_boxes = torch.zeros((len(track_instances), 4), dtype=torch.float, device=device)
        # 使用ID词典的大小，而不是num_classes
        track_instances.pred_logits = torch.zeros((len(track_instances), self.num_id_vocabulary + 1), dtype=torch.float, device=device)

        mem_bank_len = self.mem_bank_len
        track_instances.mem_bank = torch.zeros((len(track_instances), mem_bank_len, d_model), dtype=torch.float32, device=device)
        track_instances.mem_padding_mask = torch.ones((len(track_instances), mem_bank_len), dtype=torch.bool, device=device)
        track_instances.save_period = torch.zeros((len(track_instances), ), dtype=torch.float32, device=device)

        # ================== 添加新字段初始化 (CIFE, ID, Mamba States) ==================
        n_tracks = len(track_instances)

        # CIFE 特征
        track_instances.cife_features = torch.zeros((n_tracks, d_model), dtype=torch.float32, device=device)

        # ID 嵌入 (参考 MOTIP)
        track_instances.id_embeddings = torch.zeros((n_tracks, self.id_dim), dtype=torch.float32, device=device)

        # Mamba 状态 (参考 SambaMOTR)
        # 确保所有维度都是整数
        mamba_expand_int = int(self.mamba_expand)
        mamba_hidden_state_shape = (n_tracks, self.mamba_num_layers, d_model * mamba_expand_int, self.mamba_state_dim)
        track_instances.mamba_hidden_state = torch.zeros(mamba_hidden_state_shape, dtype=torch.float32, device=device)

        mamba_conv_history_shape = (n_tracks, self.mamba_num_layers, self.mamba_conv_dim, d_model * mamba_expand_int)
        track_instances.mamba_conv_history = torch.zeros(mamba_conv_history_shape, dtype=torch.float32, device=device)
        # ===========================================================================

        return track_instances.to(self.query_embed.weight.device)

    def forward(self, data_dict):
        """
        CIM Hybrid前向传播 - 高级并行处理架构
        使用因果注意力掩膜实现真正的并行化

        Args:
            data_dict: 包含'imgs'和'gt_instances'的字典
                'imgs': [B, T, C, H, W] 视频片段
                'gt_instances': [B][T] 每帧的真值标签

        Returns:
            训练模式：返回损失字典
            推理模式：返回track_instances
        """
        imgs = data_dict['imgs']  # Shape: [B, T, C, H, W]
        gt_instances_list = data_dict.get('gt_instances', None)
        B, T, C, H, W = imgs.shape
        assert B == 1, "This implementation assumes B=1."

        # ---- Step 1: 并行特征提取 ----
        flattened_imgs = nested_tensor_from_tensor_list(list(imgs.flatten(0, 1)))
        all_features, all_pos = self.backbone(flattened_imgs)

        proj_features = [self.input_proj[i](feat.tensors) for i, feat in enumerate(all_features)]

        # 使用transformer encoder处理特征 (简化版)
        # 这里需要根据实际的transformer结构调整
        encoder_out = proj_features[-1]  # [B*T, C, H, W]

        # 转换为序列格式 [B*T, HW, D]
        bt, c, h, w = encoder_out.shape
        encoder_out_seq = encoder_out.flatten(2).permute(0, 2, 1)  # [B*T, HW, D]

        # CIFE增强特征
        enhanced_memory_flat = self.cife(encoder_out_seq)  # [B*T, HW, D]

        # ---- Step 2: 并行准备 Queries (Teacher Forcing) ----
        # 使用真正的GT信息和ROI特征
        if self.training and gt_instances_list is not None:
            all_gt_queries, all_gt_roi_feats = self.prepare_clip_queries(
                gt_instances_list, enhanced_memory_flat, T, h, w
            )
            N = all_gt_queries.shape[1] // T  # 每帧的实际GT数量
        else:
            # 推理时使用固定的query数量
            N = self.num_queries
            all_gt_queries = self.query_embed.weight.unsqueeze(0).repeat(B, T, 1).view(B, T*N, -1)
            all_gt_roi_feats = torch.zeros_like(all_gt_queries)

        # ---- Step 3: 单次Mamba扫描进行时序更新 ----
        # 将queries和roi_feats结合
        mamba_input = all_gt_queries + all_gt_roi_feats  # 结合query embeddings和ROI特征
        updated_queries = self.cift.query_updater(mamba_input) if hasattr(self.cift, 'query_updater') else mamba_input  # [B, T*N, D]

        # ---- Step 4: 单次GTM并行解码 ----
        memory = enhanced_memory_flat.permute(1, 0, 2)  # [HW*T, B, D]
        queries_for_gtm = updated_queries.permute(1, 0, 2)  # [T*N, B, D]

        # 创建因果注意力掩膜
        attn_mask = self._create_causal_attention_mask(T, N, h, w, imgs.device)

        # 使用GTM进行解码 (简化版)
        if hasattr(self.cift, 'gtm'):
            decoder_output = self.cift.gtm(queries_for_gtm, memory, cross_attn_mask=attn_mask)
        else:
            # 如果没有GTM，直接使用queries
            decoder_output = queries_for_gtm

        # ---- Step 5: 预测与损失计算 ----
        pred_logits = self.class_embed(decoder_output.permute(1, 0, 2))  # [B, T*N, num_classes]
        pred_boxes = self.bbox_embed(decoder_output.permute(1, 0, 2)).sigmoid()  # [B, T*N, 4]

        # Reshape为每帧的格式
        pred_logits = pred_logits.view(B, T, N, -1)  # [B, T, N, num_classes]
        pred_boxes = pred_boxes.view(B, T, N, 4)     # [B, T, N, 4]

        # 计算损失
        if self.training and gt_instances_list is not None:
            all_losses = {}
            for t in range(T):
                frame_output = {
                    'pred_logits': pred_logits[:, t].unsqueeze(1),  # [B, 1, N, num_classes]
                    'pred_boxes': pred_boxes[:, t].unsqueeze(1)     # [B, 1, N, 4]
                }

                # 提取当前帧的GT
                frame_targets = [gt_instances_list[0][t]]  # 假设B=1

                # 计算损失
                loss_dict_t = self.criterion(frame_output, frame_targets)

                # 为损失key加上帧前缀
                for k, v in loss_dict_t.items():
                    all_losses[f'frame_{t}_{k}'] = v

            return all_losses
        else:
            # 推理模式：返回最后一帧的预测
            # 这里需要构造track_instances格式
            track_instances = self._generate_empty_tracks()
            track_instances.pred_logits = pred_logits[0, -1]  # 最后一帧
            track_instances.pred_boxes = pred_boxes[0, -1]    # 最后一帧
            return track_instances


def build(args):
    """构建CIM Hybrid模型"""
    dataset_to_num_classes = {
        'coco': 91,
        'coco_panoptic': 250,
        'e2e_mot': 1,
        'e2e_dance': 1,
        'e2e_joint': 1,
        'e2e_static_mot': 1,
    }
    assert args.dataset_file in dataset_to_num_classes
    num_classes = dataset_to_num_classes[args.dataset_file]
    device = torch.device(args.device)

    backbone = build_backbone(args)

    img_matcher = build_matcher(args)
    num_frames_per_batch = max(args.sampler_lengths)
    weight_dict = {}
    for i in range(num_frames_per_batch):
        weight_dict.update({
            'frame_{}_loss_bbox'.format(i): args.bbox_loss_coef,
            'frame_{}_loss_giou'.format(i): args.giou_loss_coef,
            'frame_{}_loss_id'.format(i): args.id_loss_coef,
        })

    memory_bank = None
    losses = ['boxes', 'id'] # 更新损失列表

    # 导入ClipMatcher
    from .motr import ClipMatcher
    criterion = ClipMatcher(num_classes, matcher=img_matcher, weight_dict=weight_dict, losses=losses, num_id_vocabulary=args.num_id_vocabulary)
    criterion.to(device)
    postprocessors = {}

    model = CIMHybrid(
        backbone,
        num_classes=num_classes,
        num_queries=args.num_queries,
        criterion=criterion,
        aux_loss=args.aux_loss,
        memory_bank=memory_bank,
        use_checkpoint=args.use_checkpoint,
        query_denoise=args.query_denoise,
        id_dim=args.id_dim,
        mamba_state_dim=args.mamba_state_dim,
        mamba_expand=args.mamba_expand,
        mamba_num_layers=args.mamba_num_layers,
        mamba_conv_dim=args.mamba_conv_dim,
        num_id_vocabulary=args.num_id_vocabulary,
    )
    return model, criterion, postprocessors
