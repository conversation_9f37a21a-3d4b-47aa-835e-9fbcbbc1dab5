# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-research. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from Deformable DETR (https://github.com/fundamentalvision/Deformable-DETR)
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

"""
CIM Hybrid Model - Transformer + Mamba 混合架构
重命名自 motr.py，现在实现 CIM-Tracker 的混合架构
"""
import copy
import math
import numpy as np
import torch
import torch.nn.functional as F
from torch import nn, Tensor
from typing import List

from util import box_ops, checkpoint
from util.misc import (NestedTensor, nested_tensor_from_tensor_list,
                       accuracy, get_world_size, interpolate, get_rank,
                       is_dist_avail_and_initialized, inverse_sigmoid)

from models.structures import Instances, Boxes, pairwise_iou, matched_boxlist_iou

from .backbone import build_backbone
from .matcher import build_matcher
from .deformable_detr.deformable_detr import SetCriterion, MLP
from .deformable_detr.segmentation import sigmoid_focal_loss
from .cife import CIFE  # 导入新的 CIFE 模块
from .cift import CIFT  # 导入重构后的 CIFT 模块
import torchvision

def pos2posemb(pos, num_pos_feats=64, temperature=10000):
    """位置编码函数，从 deformable_transformer_plus.py 复制"""
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    posemb = pos[..., None] / dim_t
    posemb = torch.stack((posemb[..., 0::2].sin(), posemb[..., 1::2].cos()), dim=-1).flatten(-3)
    return posemb


class CIMHybrid(nn.Module):
    def __init__(self, backbone, num_classes, num_queries, criterion,
                 aux_loss=True, with_box_refine=False, two_stage=False, memory_bank=None, use_checkpoint=False, query_denoise=0,
                 id_dim=256, mamba_state_dim=16, mamba_expand=2, mamba_num_layers=4, mamba_conv_dim=4, num_id_vocabulary=50):
        """ Initializes the CIM Hybrid model (Transformer + Mamba).
        Parameters:
            backbone: torch module of the backbone to be used. See backbone.py
            num_classes: number of object classes
            num_queries: number of object queries, ie detection slot. This is the maximal number of objects
                         DETR can detect in a single image. For COCO, we recommend 100 queries.
            aux_loss: True if auxiliary decoding losses (loss at each decoder layer) are to be used.
            with_box_refine: iterative bounding box refinement
            two_stage: two-stage Deformable DETR
        """
        super().__init__()
        self.num_queries = num_queries
        hidden_dim = self.hidden_dim = 256 # d_model 硬编码
        self.query_denoise = query_denoise  # 保存 query_denoise 参数

        # --- 存储 Mamba 参数 ---
        self.id_dim = id_dim
        self.mamba_state_dim = mamba_state_dim
        self.mamba_expand = mamba_expand
        self.mamba_num_layers = mamba_num_layers
        self.mamba_conv_dim = mamba_conv_dim
        self.num_id_vocabulary = num_id_vocabulary

        self.cife = CIFE(d_model=hidden_dim, n_layers=2, patch_size=8, state_dim=mamba_state_dim)
        self.cife_crop_size = (128, 64)
        self.cift = CIFT(d_model=hidden_dim)  # 使用我们重构后的 CIFT

        # 新增用于缓存的实例变量
        self.last_enhanced_features = None

        # 添加预测头
        self.class_embed = nn.Linear(hidden_dim, self.num_id_vocabulary + 1)
        self.bbox_embed = MLP(hidden_dim, hidden_dim, 4, 3)

        self.num_classes = num_classes
        self.yolox_embed = nn.Embedding(1, hidden_dim)
        self.query_embed = nn.Embedding(num_queries, hidden_dim)
        self.position = nn.Embedding(num_queries, 4)
        if query_denoise:
            self.refine_embed = nn.Embedding(1, hidden_dim)

        # DETR的输入投影层仍然需要
        num_feature_levels = 4 # 硬编码或从args传入
        self.num_feature_levels = num_feature_levels

        # 确保正确获取backbone的通道数
        if hasattr(backbone, 'num_channels'):
            backbone_channels = backbone.num_channels
        else:
            # 默认ResNet50的通道数
            backbone_channels = [2048]  # 只使用最后一层

        if num_feature_levels > 1:
            num_backbone_outs = len(backbone.strides) if hasattr(backbone, 'strides') else 1
            input_proj_list = []
            for i in range(num_backbone_outs):
                in_channels = backbone_channels[i] if i < len(backbone_channels) else backbone_channels[-1]
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
            for _ in range(num_feature_levels - num_backbone_outs):
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, stride=2, padding=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
            self.input_proj = nn.ModuleList(input_proj_list)
        else:
            # 单尺度：只使用最后一层，通道数应该是2048
            in_channels = backbone_channels[-1] if backbone_channels else 2048
            self.input_proj = nn.ModuleList([
                nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                )])
        self.backbone = backbone
        self.aux_loss = aux_loss
        self.use_checkpoint = use_checkpoint

        for proj in self.input_proj:
            nn.init.xavier_uniform_(proj[0].weight, gain=1)
            nn.init.constant_(proj[0].bias, 0)
        nn.init.uniform_(self.position.weight.data, 0, 1)

        # 导入必要的类
        from .motr import TrackerPostProcess, RuntimeTrackerBase
        self.post_process = TrackerPostProcess()
        self.track_base = RuntimeTrackerBase()
        self.criterion = criterion
        self.memory_bank = memory_bank
        self.mem_bank_len = 0 if memory_bank is None else memory_bank.max_his_length

    def _generate_empty_tracks(self, proposals=None):
        track_instances = Instances((1, 1))
        num_queries, d_model = self.query_embed.weight.shape  # (300, 256)
        device = self.query_embed.weight.device

        # 优化：避免动态分支，使用torch.compile友好的操作
        if proposals is None:
            proposals = torch.empty((0, 8), dtype=torch.float32, device=device)
        elif isinstance(proposals, list):
            if len(proposals) > 0 and isinstance(proposals[0], torch.Tensor):
                proposals = proposals[0]
            else:
                proposals = torch.empty((0, 8), dtype=torch.float32, device=device)

        # 使用torch.where避免条件分支，更compile友好
        has_proposals = proposals.numel() > 0

        if has_proposals:
            track_instances.ref_pts = torch.cat([self.position.weight, proposals[:, :4]])
            track_instances.query_pos = torch.cat([
                self.query_embed.weight,
                pos2posemb(proposals[:, 4:], d_model) + self.yolox_embed.weight
            ])
        else:
            track_instances.ref_pts = self.position.weight
            track_instances.query_pos = self.query_embed.weight
        track_instances.output_embedding = torch.zeros((len(track_instances), d_model), device=device)
        track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.disappear_time = torch.zeros((len(track_instances), ), dtype=torch.long, device=device)
        track_instances.iou = torch.ones((len(track_instances),), dtype=torch.float, device=device)
        track_instances.scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.track_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)

        # 这些将在forward中通过预测头生成，所以这里初始化为零即可
        track_instances.pred_boxes = torch.zeros((len(track_instances), 4), dtype=torch.float, device=device)
        # 使用ID词典的大小，而不是num_classes
        track_instances.pred_logits = torch.zeros((len(track_instances), self.num_id_vocabulary + 1), dtype=torch.float, device=device)

        mem_bank_len = self.mem_bank_len
        track_instances.mem_bank = torch.zeros((len(track_instances), mem_bank_len, d_model), dtype=torch.float32, device=device)
        track_instances.mem_padding_mask = torch.ones((len(track_instances), mem_bank_len), dtype=torch.bool, device=device)
        track_instances.save_period = torch.zeros((len(track_instances), ), dtype=torch.float32, device=device)

        # ================== 添加新字段初始化 (CIFE, ID, Mamba States) ==================
        n_tracks = len(track_instances)

        # CIFE 特征
        track_instances.cife_features = torch.zeros((n_tracks, d_model), dtype=torch.float32, device=device)

        # ID 嵌入 (参考 MOTIP)
        track_instances.id_embeddings = torch.zeros((n_tracks, self.id_dim), dtype=torch.float32, device=device)

        # Mamba 状态 (参考 SambaMOTR)
        # 确保所有维度都是整数
        mamba_expand_int = int(self.mamba_expand)
        mamba_hidden_state_shape = (n_tracks, self.mamba_num_layers, d_model * mamba_expand_int, self.mamba_state_dim)
        track_instances.mamba_hidden_state = torch.zeros(mamba_hidden_state_shape, dtype=torch.float32, device=device)

        mamba_conv_history_shape = (n_tracks, self.mamba_num_layers, self.mamba_conv_dim, d_model * mamba_expand_int)
        track_instances.mamba_conv_history = torch.zeros(mamba_conv_history_shape, dtype=torch.float32, device=device)
        # ===========================================================================

        return track_instances.to(self.query_embed.weight.device)

    def forward(self, data_dict):
        """
        CIM Hybrid前向传播 - 正确的多帧处理

        Args:
            data_dict: 包含'imgs'和'gt_instances'的字典
                'imgs': [B, T, C, H, W] 视频片段
                'gt_instances': [B][T] 每帧的真值标签

        Returns:
            训练模式：返回损失字典
            推理模式：返回track_instances
        """
        imgs = data_dict['imgs']
        gt_instances_all_frames = data_dict.get('gt_instances', None)

        # 确保输入有时间维度 T
        if imgs.dim() == 4:
            imgs = imgs.unsqueeze(0)

        B, T, C, H, W = imgs.shape

        # 对于追踪任务，训练时通常将 batch_size 设为 1
        assert B == 1, "Training with batch size > 1 requires careful management of track instances."

        # ================================================================
        #  Step 1: Parallel Feature Extraction (高效利用GPU)
        # ================================================================
        # 将视频片段 [B, T, C, H, W] 展平为批次 [B*T, C, H, W]
        flattened_imgs = imgs.flatten(0, 1)

        # 一次性通过 backbone 提取所有帧的特征
        flattened_nested = nested_tensor_from_tensor_list([flattened_imgs[i] for i in range(flattened_imgs.shape[0])])
        all_features, all_pos = self.backbone(flattened_nested)

        # 使用input_proj处理特征
        src, mask = all_features[-1].decompose()
        if len(self.input_proj) > 1:
            # 多尺度特征处理（简化版）
            processed_src = self.input_proj[0](src)
        else:
            processed_src = self.input_proj[0](src)

        # CIFE 也一次性增强所有帧的特征
        all_enhanced_features_flat = self.cife(processed_src)  # [B*T, C, H, W]

        # 恢复时间维度并转换为序列格式
        # [B*T, C, H, W] -> [B, T, C, H, W] -> [B, T, HW, C]
        all_enhanced_features = all_enhanced_features_flat.view(B, T, *all_enhanced_features_flat.shape[1:])
        all_enhanced_features_seq = all_enhanced_features.flatten(3).permute(0, 1, 3, 2)  # [B, T, HW, C]

        # 准备位置编码
        h, w = processed_src.shape[-2:]
        all_pos_embed = all_pos[-1].flatten(2).permute(2, 0, 1).view(T, -1, all_pos[-1].shape[1])  # [T, HW, C]

        # ================================================================
        #  Step 2: Sequential Tracking Loop (学习时序关系)
        # ================================================================
        self.last_enhanced_features = None
        track_instances = None
        all_losses = {}

        for t in range(T):
            # --- 准备当前帧的数据 ---
            # 从预先计算好的张量中切片出当前帧的特征
            # B=1, 所以我们索引 [0, t]
            current_enhanced_features = all_enhanced_features_seq[0, t].unsqueeze(1)  # Shape: [HW, 1, D]
            current_pos = all_pos_embed[t].unsqueeze(1)  # Shape: [HW, 1, D]

            # --- 初始化第一帧的轨迹 ---
            if t == 0:
                track_instances = self._generate_empty_tracks()

            # --- CIFT 进行时序更新和查询准备 ---
            track_instances = self.cift(
                track_instances=track_instances,
                current_enhanced_features=current_enhanced_features,
                last_enhanced_features=self.last_enhanced_features,
                current_pos=current_pos,
                feature_shape=(h, w)
            )

            # --- 使用预测头得到当前帧的预测 ---
            # 检查track_instances是否有query_embed字段
            if hasattr(track_instances, 'query_embed') and track_instances.query_embed is not None:
                query_features = track_instances.query_embed
            elif hasattr(track_instances, 'output_embedding') and track_instances.output_embedding is not None:
                query_features = track_instances.output_embedding
            else:
                query_features = self.query_embed.weight[:len(track_instances)]

            # 使用预测头生成当前帧的预测
            outputs_class = self.class_embed(query_features)
            outputs_coord = self.bbox_embed(query_features).sigmoid()

            # --- 格式化输出以供损失函数使用 ---
            frame_output = {
                'pred_logits': outputs_class.unsqueeze(0).unsqueeze(0),  # [1, 1, num_queries, num_classes]
                'pred_boxes': outputs_coord.unsqueeze(0).unsqueeze(0)    # [1, 1, num_queries, 4]
            }

            # --- 计算并累积当前帧的损失 ---
            if self.training and gt_instances_all_frames is not None:
                # 提取当前帧的GT
                frame_targets = [gt_instances_all_frames[0][t]]  # 假设B=1

                # 计算损失
                loss_dict_t = self.criterion(frame_output, frame_targets)

                # 为损失key加上帧前缀，以便观察
                for k, v in loss_dict_t.items():
                    all_losses[f'frame_{t}_{k}'] = v

            # --- 更新状态以备下一帧 ---
            self.last_enhanced_features = current_enhanced_features.detach()

            # 更新track_instances的预测结果
            track_instances.pred_boxes = outputs_coord
            track_instances.pred_logits = outputs_class

        # 最终返回的是所有帧损失的字典
        if self.training:
            return all_losses
        else:
            # 推理模式：返回最后一帧的track_instances
            return track_instances


def build(args):
    """构建CIM Hybrid模型"""
    dataset_to_num_classes = {
        'coco': 91,
        'coco_panoptic': 250,
        'e2e_mot': 1,
        'e2e_dance': 1,
        'e2e_joint': 1,
        'e2e_static_mot': 1,
    }
    assert args.dataset_file in dataset_to_num_classes
    num_classes = dataset_to_num_classes[args.dataset_file]
    device = torch.device(args.device)

    backbone = build_backbone(args)

    img_matcher = build_matcher(args)
    num_frames_per_batch = max(args.sampler_lengths)
    weight_dict = {}
    for i in range(num_frames_per_batch):
        weight_dict.update({
            'frame_{}_loss_bbox'.format(i): args.bbox_loss_coef,
            'frame_{}_loss_giou'.format(i): args.giou_loss_coef,
            'frame_{}_loss_id'.format(i): args.id_loss_coef,
        })

    memory_bank = None
    losses = ['boxes', 'id'] # 更新损失列表

    # 导入ClipMatcher
    from .motr import ClipMatcher
    criterion = ClipMatcher(num_classes, matcher=img_matcher, weight_dict=weight_dict, losses=losses, num_id_vocabulary=args.num_id_vocabulary)
    criterion.to(device)
    postprocessors = {}

    model = CIMHybrid(
        backbone,
        num_classes=num_classes,
        num_queries=args.num_queries,
        criterion=criterion,
        aux_loss=args.aux_loss,
        memory_bank=memory_bank,
        use_checkpoint=args.use_checkpoint,
        query_denoise=args.query_denoise,
        id_dim=args.id_dim,
        mamba_state_dim=args.mamba_state_dim,
        mamba_expand=args.mamba_expand,
        mamba_num_layers=args.mamba_num_layers,
        mamba_conv_dim=args.mamba_conv_dim,
        num_id_vocabulary=args.num_id_vocabulary,
    )
    return model, criterion, postprocessors
