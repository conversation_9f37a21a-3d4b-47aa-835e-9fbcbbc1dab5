# ====================================================================
# START: Final Implementation for cim_hybrid.py with Dual-Mode Logic
# ====================================================================
import torch
import torch.nn as nn
import math
from util.misc import NestedTensor, nested_tensor_from_tensor_list
from .backbone import build_backbone
from .deformable_detr.deformable_transformer import DeformableTransformer
from .cife import CIFE
from .cift import CIFT
from .deformable_detr.deformable_detr import MLP
from .structures.instances import Instances

def pos2posemb(pos, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x), dim=-1)
    return posemb

class CIMHybrid(nn.Module):
    """
    方案一最终版: 混合架构 (Transformer + Mamba)
    - __init__: 修正维度问题，正确初始化所有模块。
    - forward: 实现"双模"逻辑，分别处理训练和推理。
    """
    def __init__(self, backbone, transformer, num_classes, num_queries, criterion,
                 aux_loss=True, hidden_dim=256, num_id_vocabulary=500, **kwargs):
        super().__init__()
        self.backbone = backbone
        self.transformer = transformer # DeformableTransformer实例
        self.criterion = criterion
        self.hidden_dim = hidden_dim
        self.num_queries = num_queries
        self.aux_loss = aux_loss
        self.num_id_vocabulary = num_id_vocabulary
        
        # 正确构建 input_proj
        self.input_proj = nn.ModuleList()
        for in_channels in backbone.num_channels:
            self.input_proj.append(nn.Sequential(
                nn.Conv2d(in_channels, self.hidden_dim, kernel_size=1),
                nn.GroupNorm(32, self.hidden_dim)))
        
        # 初始化CIFE和CIFT
        self.cife = CIFE(d_model=self.hidden_dim)
        # CIFT内部会初始化修正后的Deformable GTM
        num_decoder_layers = getattr(transformer.decoder, 'num_layers', 6) if hasattr(transformer, 'decoder') else 6
        self.cift = CIFT(d_model=self.hidden_dim, num_decoder_layers=num_decoder_layers)
        
        # 预测头
        self.class_embed = nn.Linear(self.hidden_dim, num_id_vocabulary + 1)
        self.bbox_embed = MLP(self.hidden_dim, self.hidden_dim, 4, 3)

        # 推理时使用的组件
        try:
            from .motr import RuntimeTrackerBase
            self.track_base = RuntimeTrackerBase() # 推理时使用的轨迹管理器
        except ImportError:
            self.track_base = None
            print("⚠️  RuntimeTrackerBase 不可用，推理功能受限")
            
        self.last_enhanced_features = None # 推理时缓存特征

        # 初始化query embeddings
        self.query_embed = nn.Embedding(num_queries, hidden_dim)
        self.position = nn.Embedding(num_queries, 4)

    def _get_encoder_input(self, features):
        """准备Encoder的输入"""
        srcs = []
        masks = []
        poses = []
        for l, feat in enumerate(features):
            src, mask = feat.decompose()
            srcs.append(self.input_proj[l](src))
            masks.append(mask)
            assert mask is not None
        
        # 如果特征层数不足，需要补充
        if self.transformer.num_feature_levels > len(srcs):
            _len_srcs = len(srcs)
            for l in range(_len_srcs, self.transformer.num_feature_levels):
                if l == _len_srcs:
                    src = self.input_proj[l](features[-1].tensors)
                else:
                    src = self.input_proj[l](srcs[-1])
                m = samples.mask if 'samples' in locals() else masks[-1]
                mask = torch.nn.functional.interpolate(m[None].float(), size=src.shape[-2:]).to(torch.bool)[0]
                pos_l = self.backbone[1](NestedTensor(src, mask)).to(src.dtype)
                srcs.append(src)
                masks.append(mask)
                poses.append(pos_l)
        
        return srcs, masks, poses

    def _generate_empty_tracks(self):
        """生成空的track instances"""
        track_instances = Instances((1, 1))
        device = self.query_embed.weight.device
        d_model = self.hidden_dim
        
        track_instances.ref_pts = self.position.weight
        track_instances.query_pos = self.query_embed.weight
        track_instances.output_embedding = torch.zeros((len(track_instances), d_model), device=device)
        track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.disappear_time = torch.zeros((len(track_instances), ), dtype=torch.long, device=device)
        track_instances.iou = torch.ones((len(track_instances),), dtype=torch.float, device=device)
        track_instances.scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.track_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.pred_boxes = torch.zeros((len(track_instances), 4), dtype=torch.float, device=device)
        track_instances.pred_logits = torch.zeros((len(track_instances), self.num_id_vocabulary + 1), dtype=torch.float, device=device)

        return track_instances.to(device)

    def inference_one_frame(self, samples: NestedTensor):
        """逐帧推理逻辑"""
        # 1. 特征提取 (Backbone, Proj, Encoder, CIFE)
        features, pos = self.backbone(samples)
        srcs, masks, poses = self._get_encoder_input(features)
        
        # 简化的encoder调用
        if hasattr(self.transformer, 'encoder'):
            encoder_out = self.transformer.encoder(srcs, masks, poses)
            memory = encoder_out.get('memory', srcs[-1])
        else:
            memory = srcs[-1]
            
        current_enhanced_features = self.cife(memory)
        
        # 2. 获取或初始化轨迹
        if self.track_base and self.track_base.is_empty():
            track_instances = self._generate_empty_tracks()
        elif self.track_base:
            track_instances = self.track_base.get_active_tracks()
        else:
            track_instances = self._generate_empty_tracks()
        
        # 3. CIFT 时序更新
        track_instances = self.cift(
            track_instances=track_instances,
            current_enhanced_features=current_enhanced_features,
            last_enhanced_features=self.last_enhanced_features,
            current_pos=poses[-1] if poses else None,
            feature_shape=memory.shape[-2:]
        )
        
        # 4. 预测头
        if hasattr(track_instances, 'output_embedding'):
            query_features = track_instances.output_embedding
        else:
            query_features = track_instances.query_pos
            
        outputs_class = self.class_embed(query_features)
        outputs_coord = self.bbox_embed(query_features).sigmoid()
        
        # 5. 轨迹管理
        if self.track_base:
            self.track_base.update(outputs_class, outputs_coord)
        
        # 6. 更新缓存
        self.last_enhanced_features = current_enhanced_features.detach()
        
        if self.track_base:
            return self.track_base.get_results()
        else:
            # 简化返回
            track_instances.pred_logits = outputs_class
            track_instances.pred_boxes = outputs_coord
            return track_instances

    def forward(self, data_dict):
        if self.training:
            # ================== 训练模式: 并行提特征 + 序贯循环 ==================
            imgs = data_dict['imgs']
            gt_instances_list = data_dict.get('gt_instances', None)

            # 确保输入有时间维度
            if imgs.dim() == 4:
                imgs = imgs.unsqueeze(0)

            B, T, C, H, W = imgs.shape
            assert B == 1, "Training assumes B=1."

            # ---- 并行特征提取 ----
            flattened_imgs = nested_tensor_from_tensor_list(list(imgs.flatten(0, 1)))
            all_features, all_pos = self.backbone(flattened_imgs)

            # 处理所有特征
            all_srcs = []
            all_masks = []
            for l, feat in enumerate(all_features):
                src, mask = feat.decompose()
                all_srcs.append(self.input_proj[l](src))
                all_masks.append(mask)

            # 简化的encoder处理
            if hasattr(self.transformer, 'encoder'):
                encoder_out = self.transformer.encoder(all_srcs, all_masks, all_pos)
                enhanced_memory_flat = self.cife(encoder_out.get('memory', all_srcs[-1]))
            else:
                enhanced_memory_flat = self.cife(all_srcs[-1])

            # 恢复时间维度
            all_enhanced_features = enhanced_memory_flat.unflatten(0, (B, T))

            # ---- 序贯追踪循环 ----
            self.last_enhanced_features = None
            track_instances = None
            all_losses = {}

            for t in range(T):
                # 准备当前帧数据
                current_enhanced_features = all_enhanced_features[0, t]

                # 初始化第一帧的轨迹
                if t == 0:
                    track_instances = self._generate_empty_tracks()

                # CIFT 时序更新
                track_instances = self.cift(
                    track_instances=track_instances,
                    current_enhanced_features=current_enhanced_features,
                    last_enhanced_features=self.last_enhanced_features,
                    current_pos=all_pos[-1] if all_pos else None,
                    feature_shape=current_enhanced_features.shape[-2:]
                )

                # 预测头
                if hasattr(track_instances, 'output_embedding'):
                    query_features = track_instances.output_embedding
                else:
                    query_features = track_instances.query_pos

                outputs_class = self.class_embed(query_features)
                outputs_coord = self.bbox_embed(query_features).sigmoid()

                # 格式化输出以供损失函数使用
                frame_output = {
                    'pred_logits': outputs_class.unsqueeze(0).unsqueeze(0),  # [1, 1, num_queries, num_classes]
                    'pred_boxes': outputs_coord.unsqueeze(0).unsqueeze(0)    # [1, 1, num_queries, 4]
                }

                # 计算并累积当前帧的损失
                if gt_instances_list is not None:
                    frame_targets = [gt_instances_list[0][t]]  # 假设B=1
                    loss_dict_t = self.criterion(frame_output, frame_targets)

                    # 为损失key加上帧前缀
                    for k, v in loss_dict_t.items():
                        all_losses[f'frame_{t}_{k}'] = v

                # 更新状态以备下一帧
                self.last_enhanced_features = current_enhanced_features.detach()

                # 更新track_instances的预测结果
                track_instances.pred_boxes = outputs_coord
                track_instances.pred_logits = outputs_class

            return all_losses
        else:
            # ================== 推理模式: 调用单帧推理逻辑 ==================
            # 推理时，外部的eval_engine会逐帧送入数据
            return self.inference_one_frame(data_dict['imgs'])

# ====================================================================
# END: Final Implementation for cim_hybrid.py
# ====================================================================
