# CIFT DanceTrack 低显存配置
# 针对24GB显卡优化的配置

# 基础设置
--meta_arch cift
--dataset_file e2e_dance
--coco_path /data/workspace/detectron2/datasets/coco/
--mot_path /mnt/d/Projects/Datasets
--output_dir outputs/cift_dancetrack_low_memory

# 显存优化：减少模型规模
--num_track_slots 500  # 从500减少到200
--num_queries 300      # 从300减少到100
--hidden_dim 256

# 显存优化：减少时序长度
--sampler_steps 3      # 从5减少到3
--sampler_lengths 3    # 从5减少到3

# 显存优化：训练参数
--batch_size 1
--gradient_accumulation_steps 4  # 增加梯度累积来补偿小batch
--lr 0.0001              # 降低学习率
--lr_backbone 0.0
--epochs 50
--lr_drop 30
--lr_scheduler cosine

# 显存优化：启用所有优化技术
--use_amp True           # 混合精度
--compile_model False    # 暂时关闭编译
--use_checkpoint True    # 梯度检查点

# 模型架构
--backbone resnet50
--position_embedding sine
--enc_layers 6           # 从6减少到4
--dec_layers 6           # 从6减少到4
--dim_feedforward 1024
--dropout 0.1
--nheads 8
--num_feature_levels 4   # 从4减少到3
--dec_n_points 4
--enc_n_points 4

# 损失权重
--cls_loss_coef 2.0
--bbox_loss_coef 5.0
--giou_loss_coef 2.0
--set_cost_class 2.0
--set_cost_bbox 5.0
--set_cost_giou 2.0

# Mamba配置（减少参数）
--mamba_num_layers 2     # 从4减少到2
--mamba_state_dim 8      # 从16减少到8
--mamba_expand 2

# 数据加载
--num_workers 2
--cache_mode False

# 预训练权重
--pretrained /mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# 其他优化
--clip_max_norm 1.0
--weight_decay 0.0001
--save_period 10

# 调试和日志
--device cuda
--seed 42
