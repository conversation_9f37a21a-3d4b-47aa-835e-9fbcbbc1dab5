# ====================================================================
# Global Trajectory Module (GTM) for CIFT
# ====================================================================
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import MultiheadAttention

class GlobalTrajectoryModule(nn.Module):
    """
    Global Trajectory Module - Transformer-based localization module
    This module performs cross-attention between track queries and current frame features
    """
    def __init__(self, d_model=256, nhead=8, num_decoder_layers=6, dim_feedforward=1024, dropout=0.1):
        super().__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.num_decoder_layers = num_decoder_layers
        
        # Create decoder layers
        decoder_layer = TransformerDecoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout
        )
        self.decoder_layers = nn.ModuleList([
            TransformerDecoderLayer(d_model, nhead, dim_feedforward, dropout)
            for _ in range(num_decoder_layers)
        ])
        
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, tgt, memory, pos=None, query_pos=None):
        """
        Args:
            tgt: [num_tracks, B, D] - track queries
            memory: [HW, B, D] - current frame features
            pos: [HW, B, D] - positional encoding for memory
            query_pos: [num_tracks, B, D] - positional encoding for queries (optional)
        
        Returns:
            List of outputs from each decoder layer
        """
        output = tgt
        intermediate = []
        
        for layer in self.decoder_layers:
            output = layer(
                tgt=output,
                memory=memory,
                memory_key_padding_mask=None,
                pos=pos,
                query_pos=query_pos
            )
            intermediate.append(self.norm(output))
        
        return intermediate


class TransformerDecoderLayer(nn.Module):
    """
    Single Transformer Decoder Layer with self-attention and cross-attention
    """
    def __init__(self, d_model, nhead, dim_feedforward=1024, dropout=0.1):
        super().__init__()
        
        self.self_attn = MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=False)
        self.cross_attn = MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=False)
        
        # Feed forward network
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)
        
    def with_pos_embed(self, tensor, pos):
        return tensor if pos is None else tensor + pos
    
    def forward(self, tgt, memory, memory_key_padding_mask=None, pos=None, query_pos=None):
        # Self-attention
        q = k = self.with_pos_embed(tgt, query_pos)
        tgt2 = self.self_attn(q, k, value=tgt)[0]
        tgt = tgt + self.dropout1(tgt2)
        tgt = self.norm1(tgt)
        
        # Cross-attention
        tgt2 = self.cross_attn(
            query=self.with_pos_embed(tgt, query_pos),
            key=self.with_pos_embed(memory, pos),
            value=memory,
            key_padding_mask=memory_key_padding_mask
        )[0]
        tgt = tgt + self.dropout2(tgt2)
        tgt = self.norm2(tgt)
        
        # Feed forward
        tgt2 = self.linear2(self.dropout(F.relu(self.linear1(tgt))))
        tgt = tgt + self.dropout3(tgt2)
        tgt = self.norm3(tgt)
        
        return tgt
